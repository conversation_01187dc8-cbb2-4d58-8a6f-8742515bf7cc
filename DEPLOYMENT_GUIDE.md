# CopyTrade Deployment Guide

## Fixing OAuth Redirect Issues

### Problem
When deploying the CopyTrade application, users may experience redirect issues where OAuth callbacks redirect to `localhost` instead of the deployed URL.

### Root Cause
The issue occurs when the `NEXT_PUBLIC_APP_URL` environment variable is hardcoded to `localhost:3000` in production.

### Solution

#### 1. For Vercel Deployment
Vercel automatically provides the `VERCEL_URL` environment variable. The application will automatically detect this and use it for OAuth redirects.

**No additional configuration needed** - the app will automatically use `https://${VERCEL_URL}` for OAuth callbacks.

#### 2. For Other Hosting Platforms
Set the following environment variables in your hosting platform:

```bash
# Option 1: Set the app URL directly
NEXT_PUBLIC_APP_URL=https://your-deployed-domain.com

# Option 2: Set the site URL (alternative)
NEXT_PUBLIC_SITE_URL=https://your-deployed-domain.com
```

#### 3. For Custom Domains
If using a custom domain, update the environment variable:

```bash
NEXT_PUBLIC_APP_URL=https://your-custom-domain.com
```

### Environment Variable Priority
The application checks for URLs in this order:
1. `window.location.origin` (client-side)
2. `VERCEL_URL` (Vercel deployments)
3. `NEXT_PUBLIC_SITE_URL` (custom deployments)
4. `NEXT_PUBLIC_APP_URL` (fallback)
5. `http://localhost:3000` (development fallback)

### Supabase OAuth Configuration
Make sure your Supabase project is configured with the correct redirect URLs:

1. Go to your Supabase project dashboard
2. Navigate to Authentication > URL Configuration
3. Add your deployed domain to the "Site URL" field
4. Add your OAuth callback URLs to "Redirect URLs":
   - `https://your-domain.com/auth/callback`
   - `https://your-domain.com/auth/zerodha/callback`

### Google OAuth Configuration
Update your Google OAuth app configuration:

1. Go to Google Cloud Console
2. Navigate to APIs & Services > Credentials
3. Edit your OAuth 2.0 Client ID
4. Add your deployed domain to "Authorized JavaScript origins"
5. Add callback URLs to "Authorized redirect URIs":
   - `https://your-project.supabase.co/auth/v1/callback`

### Testing
After deployment:
1. Test Google OAuth login
2. Test Zerodha authentication
3. Test email invitations
4. Verify all redirects go to the correct domain

### Troubleshooting
If you still experience redirect issues:
1. Check browser developer tools for any hardcoded localhost URLs
2. Verify environment variables are set correctly in your hosting platform
3. Clear browser cache and cookies
4. Check Supabase and Google OAuth configurations

### Development vs Production
- **Development**: Uses `http://localhost:3000`
- **Production**: Automatically detects the deployed URL

The application now dynamically determines the correct URL based on the environment, eliminating hardcoded localhost redirects in production.
