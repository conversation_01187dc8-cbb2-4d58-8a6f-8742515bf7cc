import nodemailer from 'nodemailer';

// Email configuration type
interface EmailConfig {
  to: string;
  subject: string;
  html: string;
  from?: string;
}

// Create a transporter with environment variables
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: 587, // Standard port for TLS
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });
};

/**
 * Send an email using Nodemailer
 * @param config Email configuration object
 * @returns Promise that resolves with the send info
 */
export const sendEmail = async (config: EmailConfig) => {
  try {
    const { to, subject, html, from = process.env.EMAIL_USER } = config;

    // Create transporter
    const transporter = createTransporter();

    // Send mail
    const info = await transporter.sendMail({
      from: from || `"CopyTrade" <${process.env.EMAIL_USER}>`,
      to,
      subject,
      html,
    });

    console.log('Email sent successfully:', info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
};
