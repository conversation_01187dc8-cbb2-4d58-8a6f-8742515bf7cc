// Mock Trading Service to simulate Zerodha API while waiting for paid version
export interface MockTrade {
  id: string;
  symbol: string;
  exchange: string;
  transactionType: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  timestamp: Date;
  status: 'COMPLETE' | 'PENDING' | 'REJECTED';
  userId: string;
  userEmail: string;
  orderType: 'MARKET' | 'LIMIT';
  product: 'CNC' | 'MIS' | 'NRML';
}

export interface MockPortfolio {
  userId: string;
  holdings: {
    symbol: string;
    quantity: number;
    averagePrice: number;
    currentPrice: number;
    pnl: number;
  }[];
  totalValue: number;
  totalPnl: number;
}

class MockTradingService {
  private trades: MockTrade[] = [];
  private portfolios: Map<string, MockPortfolio> = new Map();

  // Sample stock data for realistic simulation
  private stockPrices: { [symbol: string]: number } = {
    'RELIANCE': 2450.50,
    'TCS': 3650.75,
    'INFY': 1580.25,
    'HDFCBANK': 1650.80,
    'ICICIBANK': 950.40,
    'SBIN': 580.30,
    'BHARTIARTL': 850.60,
    'ITC': 420.15,
    'KOTAKBANK': 1750.90,
    'LT': 2180.45
  };

  constructor() {
    this.loadFromStorage();
  }

  private saveToStorage() {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        localStorage.setItem('mockTrades', JSON.stringify(this.trades));
        localStorage.setItem('mockPortfolios', JSON.stringify(Array.from(this.portfolios.entries())));
      } catch (error) {
        console.error('Error saving mock data to storage:', error);
      }
    }
  }

  private loadFromStorage() {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        const savedTrades = localStorage.getItem('mockTrades');
        if (savedTrades) {
          this.trades = JSON.parse(savedTrades);
        }

        const savedPortfolios = localStorage.getItem('mockPortfolios');
        if (savedPortfolios) {
          const portfolioArray = JSON.parse(savedPortfolios);
          this.portfolios = new Map(portfolioArray);
        }
      } catch (error) {
        console.error('Error loading mock data from storage:', error);
      }
    }
  }

  // Simulate placing an order
  async placeOrder(params: {
    exchange: string;
    tradingsymbol: string;
    transaction_type: 'BUY' | 'SELL';
    quantity: number;
    price?: number;
    product: 'CNC' | 'MIS' | 'NRML';
    order_type: 'MARKET' | 'LIMIT';
    validity: 'DAY' | 'IOC';
    userId: string;
    userEmail: string;
  }): Promise<{ order_id: string; status: string }> {

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    const currentPrice = this.stockPrices[params.tradingsymbol] || 100 + Math.random() * 1000;
    const executionPrice = params.order_type === 'MARKET'
      ? currentPrice * (0.98 + Math.random() * 0.04) // ±2% slippage
      : params.price || currentPrice;

    const trade: MockTrade = {
      id: `MOCK_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      symbol: params.tradingsymbol,
      exchange: params.exchange,
      transactionType: params.transaction_type,
      quantity: params.quantity,
      price: executionPrice,
      timestamp: new Date(),
      status: Math.random() > 0.05 ? 'COMPLETE' : 'REJECTED', // 95% success rate
      userId: params.userId,
      userEmail: params.userEmail,
      orderType: params.order_type,
      product: params.product
    };

    this.trades.push(trade);

    if (trade.status === 'COMPLETE') {
      this.updatePortfolio(trade);
    }

    this.saveToStorage();

    return {
      order_id: trade.id,
      status: trade.status
    };
  }

  private updatePortfolio(trade: MockTrade) {
    let portfolio = this.portfolios.get(trade.userId);

    if (!portfolio) {
      portfolio = {
        userId: trade.userId,
        holdings: [],
        totalValue: 0,
        totalPnl: 0
      };
    }

    const existingHolding = portfolio.holdings.find(h => h.symbol === trade.symbol);

    if (existingHolding) {
      if (trade.transactionType === 'BUY') {
        const totalQuantity = existingHolding.quantity + trade.quantity;
        const totalValue = (existingHolding.quantity * existingHolding.averagePrice) + (trade.quantity * trade.price);
        existingHolding.averagePrice = totalValue / totalQuantity;
        existingHolding.quantity = totalQuantity;
      } else {
        existingHolding.quantity -= trade.quantity;
        if (existingHolding.quantity <= 0) {
          portfolio.holdings = portfolio.holdings.filter(h => h.symbol !== trade.symbol);
        }
      }
    } else if (trade.transactionType === 'BUY') {
      portfolio.holdings.push({
        symbol: trade.symbol,
        quantity: trade.quantity,
        averagePrice: trade.price,
        currentPrice: this.stockPrices[trade.symbol] || trade.price,
        pnl: 0
      });
    }

    // Update current prices and PnL
    portfolio.holdings.forEach(holding => {
      holding.currentPrice = this.stockPrices[holding.symbol] || holding.averagePrice;
      holding.pnl = (holding.currentPrice - holding.averagePrice) * holding.quantity;
    });

    portfolio.totalValue = portfolio.holdings.reduce((sum, h) => sum + (h.currentPrice * h.quantity), 0);
    portfolio.totalPnl = portfolio.holdings.reduce((sum, h) => sum + h.pnl, 0);

    this.portfolios.set(trade.userId, portfolio);
  }

  // Get user's trade history
  async getOrders(userId: string): Promise<MockTrade[]> {
    await new Promise(resolve => setTimeout(resolve, 200)); // Simulate API delay
    return this.trades.filter(trade => trade.userId === userId);
  }

  // Get user's portfolio
  async getPortfolio(userId: string): Promise<MockPortfolio | null> {
    await new Promise(resolve => setTimeout(resolve, 200)); // Simulate API delay
    return this.portfolios.get(userId) || null;
  }

  // Get all trades (for master to see what will be copied)
  async getAllTrades(): Promise<MockTrade[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return [...this.trades];
  }

  // Simulate copying trades from master to children
  async copyTradeToChildren(masterTrade: MockTrade, childUsers: { id: string; email: string }[]): Promise<void> {
    for (const child of childUsers) {
      // Create a copy of the trade for each child
      const childTrade: MockTrade = {
        ...masterTrade,
        id: `COPY_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId: child.id,
        userEmail: child.email,
        timestamp: new Date()
      };

      this.trades.push(childTrade);

      if (childTrade.status === 'COMPLETE') {
        this.updatePortfolio(childTrade);
      }
    }

    this.saveToStorage();
  }

  // Update stock prices (simulate market movement)
  updateMarketPrices() {
    Object.keys(this.stockPrices).forEach(symbol => {
      const change = (Math.random() - 0.5) * 0.02; // ±1% change
      this.stockPrices[symbol] *= (1 + change);
      this.stockPrices[symbol] = Math.round(this.stockPrices[symbol] * 100) / 100;
    });

    // Update all portfolios with new prices
    this.portfolios.forEach(portfolio => {
      portfolio.holdings.forEach(holding => {
        holding.currentPrice = this.stockPrices[holding.symbol] || holding.averagePrice;
        holding.pnl = (holding.currentPrice - holding.averagePrice) * holding.quantity;
      });

      portfolio.totalValue = portfolio.holdings.reduce((sum, h) => sum + (h.currentPrice * h.quantity), 0);
      portfolio.totalPnl = portfolio.holdings.reduce((sum, h) => sum + h.pnl, 0);
    });

    this.saveToStorage();
  }

  // Get current stock prices
  getStockPrices(): { [symbol: string]: number } {
    return { ...this.stockPrices };
  }

  // Clear all mock data (for testing)
  clearAllData() {
    this.trades = [];
    this.portfolios.clear();
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        localStorage.removeItem('mockTrades');
        localStorage.removeItem('mockPortfolios');
      } catch (error) {
        console.error('Error clearing mock data from storage:', error);
      }
    }
  }
}

// Export singleton instance
export const mockTradingService = new MockTradingService();

// Start market price updates every 30 seconds
if (typeof window !== 'undefined') {
  setInterval(() => {
    mockTradingService.updateMarketPrices();
  }, 30000);
}
