import { createClient } from '@/utils/supabase/client'

// Client-side Supabase client - use the new pattern
export const supabase = createClient()

// Legacy export for backward compatibility
export function createSupabaseBrowserClient() {
  return createClient()
}

// Types for our database
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string | null
          role: 'master' | 'child'
          created_at: string
          updated_at: string
          is_demo: boolean | null
        }
        Insert: {
          id?: string
          email: string
          name?: string | null
          role?: 'master' | 'child'
          created_at?: string
          updated_at?: string
          is_demo?: boolean | null
        }
        Update: {
          id?: string
          email?: string
          name?: string | null
          role?: 'master' | 'child'
          created_at?: string
          updated_at?: string
          is_demo?: boolean | null
        }
      }
    }
  }
}
