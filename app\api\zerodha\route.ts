import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import axios from 'axios';

// Exchange request token for access token
export async function POST(request: NextRequest) {
  try {
    const { request_token } = await request.json();

    // Validate input
    if (!request_token) {
      return NextResponse.json(
        { error: 'Request token is required' },
        { status: 400 }
      );
    }

    // Get API key and secret from environment variables
    const api_key = process.env.NEXT_PUBLIC_ZERODHA_API_KEY;
    const api_secret = process.env.ZERODHA_API_SECRET;

    if (!api_key || !api_secret) {
      return NextResponse.json(
        { error: 'API credentials not found in environment variables' },
        { status: 500 }
      );
    }

    try {
      // Generate checksum as per Zerodha docs: SHA-256 of (api_key + request_token + api_secret)
      const checksum = crypto
        .createHash('sha256')
        .update(api_key + request_token + api_secret)
        .digest('hex');

      // Create form data for the request
      const formData = new URLSearchParams();
      formData.append('api_key', api_key);
      formData.append('request_token', request_token);
      formData.append('checksum', checksum);

      // Exchange request token for access token
      const response = await axios.post(
        'https://api.kite.trade/session/token',
        formData.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      console.log('Zerodha API response:', response.data);

      // Return access token and other data
      return NextResponse.json({
        success: true,
        access_token: response.data.data.access_token,
        // Include any other data from the response that might be useful
        user_data: response.data.data,
        public_token: response.data.data.public_token,
      });
    } catch (apiError: any) {
      console.error('Zerodha API error:', apiError.response?.data || apiError.message);
      return NextResponse.json(
        {
          error: 'Failed to exchange token with Zerodha API',
          details: apiError.response?.data || apiError.message
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Token exchange error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Refresh access token using refresh token
export async function PUT(request: NextRequest) {
  try {
    const { refreshToken } = await request.json();

    // Validate input
    if (!refreshToken) {
      return NextResponse.json(
        { error: 'Refresh token is required' },
        { status: 400 }
      );
    }

    // Get API key and secret from environment variables
    const apiKey = process.env.NEXT_PUBLIC_ZERODHA_API_KEY;
    const apiSecret = process.env.ZERODHA_API_SECRET;

    if (!apiKey || !apiSecret) {
      return NextResponse.json(
        { error: 'API credentials not found in environment variables' },
        { status: 500 }
      );
    }

    try {
      // Generate checksum
      const checksum = crypto
        .createHash('sha256')
        .update(apiKey + refreshToken + apiSecret)
        .digest('hex');

      // Refresh access token
      const response = await axios.post(
        'https://api.kite.trade/session/refresh_token',
        {
          api_key: apiKey,
          refresh_token: refreshToken,
          checksum,
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      // Return new access token
      return NextResponse.json({
        success: true,
        accessToken: response.data.access_token,
        // Include any other data from the response that might be useful
        userData: response.data.data,
      });
    } catch (apiError: any) {
      console.error('Zerodha API error:', apiError.response?.data || apiError.message);
      return NextResponse.json(
        {
          error: 'Failed to refresh token with Zerodha API',
          details: apiError.response?.data || apiError.message
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Token refresh error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
