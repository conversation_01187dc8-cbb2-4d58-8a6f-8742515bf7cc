'use client';

import React from 'react';
import Link from 'next/link';
import { useAuth } from '@/app/context/AuthContext';
import Button from './Button';
import ProfileButton from './ProfileButton';

const Header: React.FC = () => {
  const { user, logout, isAuthenticated } = useAuth();

  return (
    <header className="bg-background shadow-sm border-b border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link href="/" className="text-xl font-bold text-blue-600 dark:text-blue-400">
                CopyTrade
              </Link>
            </div>
            <nav className="ml-6 flex space-x-8">
              {isAuthenticated && (
                <>
                  {user?.role === 'master' ? (
                    <>
                      <Link href="/master/dashboard" className="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-muted-foreground hover:text-foreground hover:border-border">
                        Dashboard
                      </Link>
                      <Link href="/master/configuration" className="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-muted-foreground hover:text-foreground hover:border-border">
                        Configuration
                      </Link>
                      <Link href="/demo/trading" className="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-muted-foreground hover:text-foreground hover:border-border">
                        Demo Trade
                      </Link>
                    </>
                  ) : (
                    <Link href="/child/dashboard" className="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-muted-foreground hover:text-foreground hover:border-border">
                      Dashboard
                    </Link>
                  )}
                </>
              )}
            </nav>
          </div>
          <div className="flex items-center">
            {isAuthenticated ? (
              <ProfileButton />
            ) : (
              <div className="space-x-4">
                <Link href="/">
                  <Button size="sm">
                    Select Role
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
