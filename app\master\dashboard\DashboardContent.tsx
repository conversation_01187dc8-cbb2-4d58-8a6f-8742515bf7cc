'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { GlowingBorder } from '@/app/components/ui/animated-border';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  PieChart,
  Activity,
  BarChart3,
  ArrowUpRight,
  ArrowDownRight,
  Wallet,
  Target,
  Clock,
  Users
} from 'lucide-react';

// Mock data for portfolio management dashboard
const mockPortfolioData = {
  totalValue: 2847650.50,
  totalPnL: 184750.25,
  dayChange: 12450.75,
  dayChangePercent: 2.34,
  holdings: [
    { symbol: 'RELIANCE', quantity: 100, currentPrice: 2456.75, pnl: 12450.50, dayChange: 234.25 },
    { symbol: 'TCS', quantity: 50, currentPrice: 3567.80, pnl: -5670.25, dayChange: -123.45 },
    { symbol: 'INFY', quantity: 75, currentPrice: 1789.60, pnl: 8920.75, dayChange: 89.30 },
    { symbol: 'HDFC', quantity: 200, currentPrice: 1654.30, pnl: 15670.80, dayChange: 456.70 }
  ],
  recentTrades: [
    { symbol: 'RELIANCE', type: 'BUY', quantity: 10, price: 2456.75, time: '10:30 AM' },
    { symbol: 'TCS', type: 'SELL', quantity: 5, price: 3567.80, time: '11:15 AM' },
    { symbol: 'INFY', type: 'BUY', quantity: 15, price: 1789.60, time: '2:45 PM' }
  ]
};

export default function DashboardContent() {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      {/* Animated background gradient */}
      <div className="fixed inset-0 bg-gradient-to-br from-blue-50/50 via-background to-purple-50/50 dark:from-blue-950/10 dark:via-background dark:to-purple-950/10" />

      {/* Animated gradient overlay */}
      <motion.div
        className="fixed inset-0 opacity-30 dark:opacity-20 pointer-events-none"
        animate={{
          background: [
            "radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 80% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 50% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)"
          ]
        }}
        transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
      />

      {/* Floating orbs */}
      <motion.div
        className="fixed top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/30 to-purple-400/30 dark:from-blue-400/20 dark:to-purple-400/20 rounded-full blur-xl pointer-events-none"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
        }}
        transition={{ duration: 6, repeat: Infinity, ease: "easeInOut" }}
      />
      <motion.div
        className="fixed top-40 right-20 w-24 h-24 bg-gradient-to-r from-purple-400/30 to-pink-400/30 dark:from-purple-400/20 dark:to-pink-400/20 rounded-full blur-xl pointer-events-none"
        animate={{
          y: [0, 20, 0],
          x: [0, -15, 0],
        }}
        transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
      />

      <main className="flex-grow relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <motion.div
            className="flex items-center justify-between mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.h1
              className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Portfolio Dashboard
            </motion.h1>
          </motion.div>

          {/* Portfolio Overview Cards - Bento Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          >
            {/* Total Portfolio Value */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <GlowingBorder glowColor="blue">
                <Card className="border-0 bg-background/50 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                        <p className="text-2xl font-bold text-foreground">
                          ₹{mockPortfolioData.totalValue.toLocaleString()}
                        </p>
                      </div>
                      <div className="p-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg">
                        <Wallet className="w-6 h-6 text-blue-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>

            {/* Total P&L */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <GlowingBorder glowColor={mockPortfolioData.totalPnL >= 0 ? "cyan" : "pink"}>
                <Card className="border-0 bg-background/50 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total P&L</p>
                        <p className={`text-2xl font-bold ${mockPortfolioData.totalPnL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {mockPortfolioData.totalPnL >= 0 ? '+' : ''}₹{mockPortfolioData.totalPnL.toLocaleString()}
                        </p>
                      </div>
                      <div className={`p-3 rounded-lg ${mockPortfolioData.totalPnL >= 0 ? 'bg-gradient-to-r from-green-500/10 to-emerald-500/10' : 'bg-gradient-to-r from-red-500/10 to-pink-500/10'}`}>
                        {mockPortfolioData.totalPnL >= 0 ?
                          <TrendingUp className="w-6 h-6 text-green-600" /> :
                          <TrendingDown className="w-6 h-6 text-red-600" />
                        }
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>

            {/* Day Change */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <GlowingBorder glowColor={mockPortfolioData.dayChange >= 0 ? "cyan" : "pink"}>
                <Card className="border-0 bg-background/50 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Day Change</p>
                        <p className={`text-2xl font-bold ${mockPortfolioData.dayChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {mockPortfolioData.dayChange >= 0 ? '+' : ''}₹{mockPortfolioData.dayChange.toLocaleString()}
                        </p>
                        <p className={`text-sm ${mockPortfolioData.dayChangePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {mockPortfolioData.dayChangePercent >= 0 ? '+' : ''}{mockPortfolioData.dayChangePercent}%
                        </p>
                      </div>
                      <div className={`p-3 rounded-lg ${mockPortfolioData.dayChange >= 0 ? 'bg-gradient-to-r from-green-500/10 to-emerald-500/10' : 'bg-gradient-to-r from-red-500/10 to-pink-500/10'}`}>
                        {mockPortfolioData.dayChange >= 0 ?
                          <ArrowUpRight className="w-6 h-6 text-green-600" /> :
                          <ArrowDownRight className="w-6 h-6 text-red-600" />
                        }
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>

            {/* Active Holdings */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <GlowingBorder glowColor="purple">
                <Card className="border-0 bg-background/50 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Holdings</p>
                        <p className="text-2xl font-bold text-foreground">
                          {mockPortfolioData.holdings.length}
                        </p>
                        <p className="text-sm text-muted-foreground">Active stocks</p>
                      </div>
                      <div className="p-3 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg">
                        <PieChart className="w-6 h-6 text-purple-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>
          </motion.div>

          {/* Holdings Table */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mb-8"
          >
            <GlowingBorder className="h-full" glowColor="blue">
              <Card className="border-0 bg-background/50 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <motion.div
                      className="p-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <BarChart3 className="w-6 h-6 text-blue-600" />
                    </motion.div>
                    <div>
                      <CardTitle className="text-xl font-semibold text-foreground">Portfolio Holdings</CardTitle>
                      <CardDescription className="text-muted-foreground">
                        Your current stock positions
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockPortfolioData.holdings.map((holding, index) => (
                      <motion.div
                        key={holding.symbol}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.1 }}
                        className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-500/5 to-slate-500/5 rounded-lg border border-border/50"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="p-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg">
                            <Target className="w-5 h-5 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-semibold text-foreground">{holding.symbol}</p>
                            <p className="text-sm text-muted-foreground">Qty: {holding.quantity}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-foreground">₹{holding.currentPrice.toLocaleString()}</p>
                          <p className={`text-sm ${holding.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {holding.pnl >= 0 ? '+' : ''}₹{holding.pnl.toLocaleString()}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </GlowingBorder>
          </motion.div>

          {/* Recent Trades and Market Data Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Recent Trades */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
            >
              <GlowingBorder className="h-full" glowColor="purple">
                <Card className="border-0 bg-background/50 backdrop-blur-sm h-full">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <motion.div
                        className="p-3 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg"
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Activity className="w-6 h-6 text-purple-600" />
                      </motion.div>
                      <div>
                        <CardTitle className="text-xl font-semibold text-foreground">Recent Trades</CardTitle>
                        <CardDescription className="text-muted-foreground">
                          Latest trading activity
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {mockPortfolioData.recentTrades.map((trade, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.1 }}
                          className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-500/5 to-slate-500/5 rounded-lg border border-border/50"
                        >
                          <div className="flex items-center space-x-3">
                            <div className={`p-2 rounded-lg ${trade.type === 'BUY' ? 'bg-gradient-to-r from-green-500/10 to-emerald-500/10' : 'bg-gradient-to-r from-red-500/10 to-pink-500/10'}`}>
                              {trade.type === 'BUY' ?
                                <ArrowUpRight className="w-4 h-4 text-green-600" /> :
                                <ArrowDownRight className="w-4 h-4 text-red-600" />
                              }
                            </div>
                            <div>
                              <p className="font-semibold text-foreground">{trade.symbol}</p>
                              <p className="text-sm text-muted-foreground">{trade.type} {trade.quantity}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold text-foreground">₹{trade.price.toLocaleString()}</p>
                            <p className="text-sm text-muted-foreground">{trade.time}</p>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>

            {/* Market Data Widget */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
            >
              <GlowingBorder className="h-full" glowColor="cyan">
                <Card className="border-0 bg-background/50 backdrop-blur-sm h-full">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <motion.div
                        className="p-3 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-lg"
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <BarChart3 className="w-6 h-6 text-cyan-600" />
                      </motion.div>
                      <div>
                        <CardTitle className="text-xl font-semibold text-foreground">Market Overview</CardTitle>
                        <CardDescription className="text-muted-foreground">
                          Key market indicators
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-500/5 to-slate-500/5 rounded-lg border border-border/50">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg">
                            <TrendingUp className="w-4 h-4 text-green-600" />
                          </div>
                          <div>
                            <p className="font-semibold text-foreground">NIFTY 50</p>
                            <p className="text-sm text-muted-foreground">NSE</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-foreground">19,674.25</p>
                          <p className="text-sm text-green-600">+234.50 (+1.21%)</p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-500/5 to-slate-500/5 rounded-lg border border-border/50">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-gradient-to-r from-red-500/10 to-pink-500/10 rounded-lg">
                            <TrendingDown className="w-4 h-4 text-red-600" />
                          </div>
                          <div>
                            <p className="font-semibold text-foreground">SENSEX</p>
                            <p className="text-sm text-muted-foreground">BSE</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-foreground">65,832.10</p>
                          <p className="text-sm text-red-600">-123.45 (-0.19%)</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </GlowingBorder>
            </motion.div>
          </div>
        </div>
      </main>
    </div>
  );
}
