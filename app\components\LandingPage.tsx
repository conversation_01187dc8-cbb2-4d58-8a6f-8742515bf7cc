'use client';

import { useAuth } from '../context/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import HeroSection from './landing/HeroSection';
import FeaturesSection from './landing/FeaturesSection';
import TestimonialsSection from './landing/TestimonialsSection';
import PricingSection from './landing/PricingSection';
import FAQSection from './landing/FAQSection';
import Footer from './landing/Footer';

export default function LandingPage() {
  const { isAuthenticated, user, handlePostAuthRedirect } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [redirectMessage, setRedirectMessage] = useState<string | null>(null);

  // Check for redirect parameter and show appropriate message
  useEffect(() => {
    const redirectPath = searchParams.get('redirect');
    if (redirectPath) {
      setRedirectMessage(`Please sign in to access ${redirectPath}`);
    }
  }, [searchParams]);

  // Handle redirect after authentication
  useEffect(() => {
    if (isAuthenticated && user && typeof window !== 'undefined') {
      const redirectPath = searchParams.get('redirect');
      if (redirectPath && window.location.pathname === '/') {
        // Only handle redirect if we're on the landing page
        handlePostAuthRedirect();
      }
    }
  }, [isAuthenticated, user, searchParams, handlePostAuthRedirect]);

  const handleGetStarted = () => {
    if (isAuthenticated) {
      router.push(user?.role === 'master' ? '/master/dashboard' : '/child/dashboard');
    } else {
      router.push('/auth/register');
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {redirectMessage && (
        <div className="bg-blue-50 dark:bg-blue-950/20 border-l-4 border-blue-400 p-4 mb-4">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {redirectMessage}
              </p>
            </div>
          </div>
        </div>
      )}
      <HeroSection
        isAuthenticated={isAuthenticated}
        user={user}
        onGetStarted={handleGetStarted}
      />
      <div id="features">
        <FeaturesSection />
      </div>
      <TestimonialsSection />
      <div id="pricing">
        <PricingSection />
      </div>
      <div id="faq">
        <FAQSection />
      </div>
      <Footer />
    </div>
  );
}
