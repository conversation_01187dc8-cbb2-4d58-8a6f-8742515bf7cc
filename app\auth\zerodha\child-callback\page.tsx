'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import axios from 'axios';
import Button from '@/app/components/Button';

function ChildZerodhaCallbackContent() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const request_token = searchParams.get('request_token');
        const status = searchParams.get('status');
        const redirect_params = searchParams.get('redirect_params');

        console.log('Child OAuth callback params:', { request_token, status, redirect_params });

        if (status === 'success' && request_token) {
          try {
            // Exchange request token for access token
            const response = await axios.post('/api/zerodha', {
              request_token
            });

            if (response.data.success) {
              console.log('Child token exchange successful');

              // Get invitation data from localStorage
              const invitationDataStr = localStorage.getItem('invitation_data');
              if (!invitationDataStr) {
                throw new Error('Invitation data not found');
              }

              const invitationData = JSON.parse(invitationDataStr);

              // Store access token temporarily to fetch profile
              localStorage.setItem('zerodha_access_token', response.data.access_token);

              // Fetch user profile from Zerodha to get email
              const profileResponse = await axios.get('/api/zerodha/proxy', {
                params: {
                  endpoint: 'user/profile',
                  access_token: response.data.access_token
                }
              });

              const zerodhaEmail = profileResponse.data.data.email;
              const zerodhaUserId = profileResponse.data.data.user_id;

              console.log('Child Zerodha profile:', { zerodhaEmail, zerodhaUserId });

              // Create child user with Zerodha data
              const childUser = {
                id: Math.random().toString(36).substring(2, 9),
                email: zerodhaEmail,
                role: 'child' as const,
                name: profileResponse.data.data.user_name || 'Child User',
                zerodhaAccessToken: response.data.access_token,
                zerodhaUserId: zerodhaUserId,
                masterId: invitationData.masterId,
                masterEmail: invitationData.masterEmail,
                invitedEmail: invitationData.childEmail
              };

              // Store child user in localStorage
              localStorage.setItem('user', JSON.stringify(childUser));

              // Store refresh token if available
              if (response.data.refresh_token) {
                localStorage.setItem('zerodhaRefreshToken', response.data.refresh_token);
              }

              // Store public token if available
              if (response.data.public_token) {
                localStorage.setItem('zerodha_public_token', response.data.public_token);
              }

              // Register child user in the backend
              try {
                const registerResponse = await fetch('/api/auth/child-register', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    childUser: childUser,
                    invitationData: invitationData
                  }),
                });

                if (!registerResponse.ok) {
                  console.warn('Failed to register child user in backend, but continuing with local storage');
                }
              } catch (registerError) {
                console.warn('Backend registration failed:', registerError);
              }

              // Clean up invitation data
              localStorage.removeItem('invitation_data');

              setStatus('success');
              setMessage('Successfully connected to Zerodha! You are now linked as a child user.');

            } else {
              throw new Error('Token exchange failed');
            }
          } catch (error) {
            console.error('Child OAuth error:', error);
            setStatus('error');
            setError('Failed to connect to Zerodha. Please try again.');
          }
        } else {
          setStatus('error');
          setError('OAuth authentication was cancelled or failed.');
        }
      } catch (error) {
        console.error('Callback handling error:', error);
        setStatus('error');
        setError('An unexpected error occurred during authentication.');
      }
    };

    handleCallback();
  }, [searchParams]);

  const handleContinue = () => {
    router.push('/child/dashboard');
  };

  const handleRetry = () => {
    // Get invitation data and retry OAuth
    const invitationDataStr = localStorage.getItem('invitation_data');
    if (invitationDataStr) {
      const invitationData = JSON.parse(invitationDataStr);

      // Get API key from environment variable
      const apiKey = process.env.NEXT_PUBLIC_ZERODHA_API_KEY;

      if (!apiKey) {
        console.error('Zerodha API key not found in environment variables');
        return;
      }

      // Create redirect parameters for child OAuth flow
      const redirectParams = encodeURIComponent(JSON.stringify({
        master_id: invitationData.masterId,
        child_email: invitationData.childEmail,
        flow_type: 'child_oauth'
      }));

      // Redirect to Zerodha login page
      window.location.href = `https://kite.zerodha.com/connect/login?v=3&api_key=${apiKey}&redirect_params=${redirectParams}`;
    } else {
      router.push('/');
    }
  };

  if (status === 'loading') {
    return (
      <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center">
        <h1 className="text-2xl font-bold mb-6">Connecting to Zerodha</h1>
        <div className="flex justify-center mb-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
        <p className="text-gray-600">Please wait while we connect your account...</p>
      </div>
    );
  }

  if (status === 'success') {
    return (
      <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center">
        <div className="mb-6">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
            <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
        </div>
        <h1 className="text-2xl font-bold text-green-600 mb-4">Connection Successful!</h1>
        <p className="text-gray-600 mb-6">{message}</p>
        <Button onClick={handleContinue} className="w-full">
          Continue to Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center">
      <div className="mb-6">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
          <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </div>
      </div>
      <h1 className="text-2xl font-bold text-red-600 mb-4">Connection Failed</h1>
      <p className="text-gray-600 mb-6">{error}</p>
      <div className="space-y-3">
        <Button onClick={handleRetry} className="w-full">
          Try Again
        </Button>
        <Button onClick={() => router.push('/')} variant="outline" className="w-full">
          Return to Home
        </Button>
      </div>
    </div>
  );
}

export default function ChildZerodhaCallbackPage() {
  return (
    <Suspense fallback={
      <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center">
        <h1 className="text-2xl font-bold mb-6">Loading...</h1>
        <div className="flex justify-center mb-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
        <p className="text-gray-600">Please wait...</p>
      </div>
    }>
      <ChildZerodhaCallbackContent />
    </Suspense>
  );
}
