# CopyTrade Demo Mode

## Overview

Demo Mode allows you to test the complete CopyTrade application without real Zerodha API limitations or real money. This is perfect for:

- **Development & Testing**: Test all features safely
- **User Onboarding**: Let users experience the app before real trading
- **API Limitations**: Work around Zerodha's single-user free API limitation
- **Demonstrations**: Show the app to potential users/investors

## 🚀 Features

### ✅ What Works in Demo Mode

1. **Complete Authentication Flow**
   - Master and child user registration
   - Mock Zerodha OAuth (no real API needed)
   - Email invitations with demo indicators

2. **Realistic Trading Simulation**
   - Live stock price updates (every 30 seconds)
   - Order placement with 95% success rate
   - Portfolio tracking with P&L calculations
   - Market hours simulation

3. **Master-Child Copy Trading**
   - Master trades automatically copied to children
   - Real-time portfolio synchronization
   - Trade history tracking

4. **Full UI Experience**
   - All dashboards and components work
   - Demo mode indicators throughout
   - Realistic data and interactions

### 🎯 Demo Mode Benefits

- **No API Limitations**: Unlimited users can test
- **No Real Money**: 100% safe environment
- **Full Feature Set**: Experience complete workflow
- **Easy Toggle**: Switch to real mode when ready

## 🛠️ Setup & Configuration

### Enable Demo Mode

Edit `app/config/demoMode.ts`:

```typescript
export const DEMO_MODE = {
  enabled: true,  // Set to false for production
  // ... other settings
};
```

### Key Configuration Options

```typescript
settings: {
  simulateApiDelay: true,        // Realistic API delays
  showDemoIndicators: true,      // Show demo badges/warnings
  allowMockAuth: true,           // Enable mock Zerodha auth
  generateDemoData: true,        // Auto-generate sample data
}
```

## 📧 Email Invitations

Demo mode enhances email invitations with:

- **Demo Mode Indicators**: Clear messaging about simulation
- **Safe Environment**: Emphasizes no real money involved
- **Full Workflow**: Complete invitation → authentication → trading flow

## 🔄 Switching to Production

When you get the paid Zerodha API:

1. **Disable Demo Mode**:
   ```typescript
   // app/config/demoMode.ts
   enabled: false
   ```

2. **Update Environment Variables**:
   ```env
   NEXT_PUBLIC_ZERODHA_API_KEY=your_paid_api_key
   ZERODHA_API_SECRET=your_paid_api_secret
   ```

3. **Test Real API**: Verify with small trades first

## 🎮 Demo Trading Features

### Stock Price Simulation
- **10 Popular Stocks**: RELIANCE, TCS, INFY, etc.
- **Live Updates**: Prices change every 30 seconds
- **Realistic Movement**: ±2% price changes
- **Market Hours**: Simulates 9 AM - 3:30 PM trading

### Order Execution
- **95% Success Rate**: Realistic order failures
- **Market Slippage**: ±2% execution variance
- **Order Types**: MARKET, LIMIT orders supported
- **Instant Execution**: Fast feedback for testing

### Portfolio Management
- **Real-time P&L**: Live profit/loss calculations
- **Holdings Tracking**: Quantity, average price, current value
- **Trade History**: Complete order history
- **Copy Trading**: Master trades auto-copied to children

## 🧪 Testing Scenarios

### Master User Flow
1. Register as master user
2. Connect to "Zerodha" (mock auth)
3. Invite child users via email
4. Place demo trades
5. See trades copied to children

### Child User Flow
1. Receive invitation email
2. Accept invitation
3. Connect to "Zerodha" (mock auth)
4. View copied trades in portfolio
5. See real-time updates

### Copy Trading Test
1. Master places BUY order for RELIANCE
2. Order executes successfully
3. All connected children receive same trade
4. Portfolios update across all users
5. P&L calculated for everyone

## 📱 UI Components

### Demo Mode Indicators
- **Banner**: Top of pages showing demo status
- **Badges**: Small indicators in navigation
- **Warnings**: Before trading actions
- **Features**: Showcase demo capabilities

### Demo Trading Page
- **Interactive Trading**: Place orders with real UI
- **Live Portfolio**: See holdings and P&L
- **Order History**: Track all demo trades
- **Market Data**: Live price updates

## 🔧 Technical Implementation

### Key Files
- `app/config/demoMode.ts` - Configuration
- `app/utils/mockTradingService.ts` - Trading simulation
- `app/utils/authService.ts` - Enhanced auth with demo support
- `app/utils/enhancedTradingService.ts` - Unified trading service
- `app/components/DemoModeIndicator.tsx` - UI components

### Data Storage
- **LocalStorage**: Persistent demo data
- **In-Memory**: Real-time price updates
- **Mock APIs**: Simulated Zerodha responses

## 🚨 Important Notes

### Security
- Demo mode is clearly indicated everywhere
- No real API keys exposed in demo
- Safe for public demonstrations

### Performance
- Efficient local storage usage
- Minimal memory footprint
- Real-time updates without API calls

### Limitations
- Data resets on browser clear
- Single browser session
- No real market data

## 🎯 Next Steps

1. **Test the Demo**: Try all features in demo mode
2. **Share with Users**: Let others test the application
3. **Gather Feedback**: Improve based on demo usage
4. **Prepare for Production**: When Zerodha API is ready

## 🆘 Troubleshooting

### Demo Mode Not Working
- Check `isDemoMode()` returns `true`
- Verify configuration in `demoMode.ts`
- Clear browser storage and retry

### Orders Not Executing
- Check user authentication
- Verify mock trading service
- Look for console errors

### Prices Not Updating
- Check if market simulation is running
- Verify 30-second update interval
- Refresh page to restart updates

---

**Ready to test?** Start with the master dashboard and invite some demo users! 🚀
