// Enhanced Authentication Service with Demo Mode Support
import { isDemoMode, mockZerodhaAuth, canUseMockAuth } from '../config/demoMode';

export interface AuthUser {
  id: string;
  email: string;
  role: 'master' | 'child';
  name?: string;
  zerodhaAccessToken?: string;
  zerodhaRefreshToken?: string;
  zerodhaUserId?: string;
  masterId?: string;
  isDemo?: boolean;
}

class AuthService {
  private currentUser: AuthUser | null = null;

  constructor() {
    this.loadUserFromStorage();
  }

  private saveUserToStorage(user: AuthUser | null) {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        if (user) {
          localStorage.setItem('user', JSON.stringify(user));
          localStorage.setItem('zerodha_access_token', user.zerodhaAccessToken || '');
          localStorage.setItem('zerodhaRefreshToken', user.zerodhaRefreshToken || '');
        } else {
          localStorage.removeItem('user');
          localStorage.removeItem('zerodha_access_token');
          localStorage.removeItem('zerodhaRefreshToken');
        }
      } catch (error) {
        console.error('Error saving user to storage:', error);
      }
    }
  }

  private loadUserFromStorage() {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          this.currentUser = JSON.parse(userStr);
        }
      } catch (error) {
        console.error('Error loading user from storage:', error);
      }
    }
  }

  // Enhanced Zerodha authentication with demo mode support
  async authenticateWithZerodha(requestToken: string, userEmail?: string): Promise<AuthUser> {
    try {
      if (isDemoMode() && canUseMockAuth()) {
        // Use mock authentication in demo mode
        return this.mockZerodhaAuthentication(userEmail || '<EMAIL>');
      }

      // Real Zerodha authentication
      const response = await fetch('/api/zerodha', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ request_token: requestToken }),
      });

      if (!response.ok) {
        throw new Error('Failed to authenticate with Zerodha');
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Authentication failed');
      }

      // Create user object
      const user: AuthUser = {
        id: data.userData?.user_id || `user_${Date.now()}`,
        email: data.userData?.email || userEmail || '<EMAIL>',
        role: 'master', // Default role, can be changed based on invitation
        name: data.userData?.user_name,
        zerodhaAccessToken: data.access_token,
        zerodhaRefreshToken: data.refresh_token,
        zerodhaUserId: data.userData?.user_id,
        isDemo: false
      };

      this.currentUser = user;
      this.saveUserToStorage(user);

      return user;
    } catch (error) {
      console.error('Zerodha authentication error:', error);
      throw error;
    }
  }

  // Mock authentication for demo mode
  private async mockZerodhaAuthentication(email: string): Promise<AuthUser> {
    const mockData = await mockZerodhaAuth(email);

    const user: AuthUser = {
      id: mockData.user_id,
      email: mockData.email,
      role: 'master',
      name: mockData.user_name,
      zerodhaAccessToken: mockData.access_token,
      zerodhaRefreshToken: mockData.refresh_token,
      zerodhaUserId: mockData.user_id,
      isDemo: true
    };

    this.currentUser = user;
    this.saveUserToStorage(user);

    return user;
  }

  // Child user authentication with invitation
  async authenticateChildWithInvitation(
    requestToken: string,
    invitationData: any
  ): Promise<AuthUser> {
    try {
      if (isDemoMode() && canUseMockAuth()) {
        // Mock child authentication
        const mockData = await mockZerodhaAuth(invitationData.childEmail);

        const childUser: AuthUser = {
          id: mockData.user_id,
          email: mockData.email,
          role: 'child',
          name: mockData.user_name,
          zerodhaAccessToken: mockData.access_token,
          zerodhaRefreshToken: mockData.refresh_token,
          zerodhaUserId: mockData.user_id,
          masterId: invitationData.masterId,
          isDemo: true
        };

        this.currentUser = childUser;
        this.saveUserToStorage(childUser);

        return childUser;
      }

      // Real child authentication (when you get paid API)
      const response = await fetch('/api/zerodha', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ request_token: requestToken }),
      });

      if (!response.ok) {
        throw new Error('Failed to authenticate child with Zerodha');
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Child authentication failed');
      }

      const childUser: AuthUser = {
        id: data.userData?.user_id || `child_${Date.now()}`,
        email: invitationData.childEmail,
        role: 'child',
        name: data.userData?.user_name,
        zerodhaAccessToken: data.access_token,
        zerodhaRefreshToken: data.refresh_token,
        zerodhaUserId: data.userData?.user_id,
        masterId: invitationData.masterId,
        isDemo: false
      };

      this.currentUser = childUser;
      this.saveUserToStorage(childUser);

      return childUser;
    } catch (error) {
      console.error('Child authentication error:', error);
      throw error;
    }
  }

  // Get current user
  getCurrentUser(): AuthUser | null {
    return this.currentUser;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return this.currentUser !== null;
  }

  // Check if user is connected to Zerodha
  isConnectedToZerodha(): boolean {
    return this.currentUser?.zerodhaAccessToken ? true : false;
  }

  // Logout
  logout() {
    this.currentUser = null;
    this.saveUserToStorage(null);
  }

  // Update user data
  updateUser(updates: Partial<AuthUser>) {
    if (this.currentUser) {
      this.currentUser = { ...this.currentUser, ...updates };
      this.saveUserToStorage(this.currentUser);
    }
  }

  // Refresh Zerodha token
  async refreshZerodhaToken(): Promise<string> {
    if (!this.currentUser?.zerodhaRefreshToken) {
      throw new Error('No refresh token available');
    }

    if (isDemoMode()) {
      // Mock token refresh
      const newToken = `mock_access_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      this.updateUser({ zerodhaAccessToken: newToken });
      return newToken;
    }

    // Real token refresh
    const response = await fetch('/api/zerodha', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        refreshToken: this.currentUser.zerodhaRefreshToken
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to refresh token');
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || 'Token refresh failed');
    }

    this.updateUser({ zerodhaAccessToken: data.accessToken });
    return data.accessToken;
  }

  // Get demo mode status
  isDemoMode(): boolean {
    return this.currentUser?.isDemo || false;
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
