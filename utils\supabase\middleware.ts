import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  const {
    data: { user },
  } = await supabase.auth.getUser()

  const { pathname } = request.nextUrl

  // Define protected routes that require authentication
  const protectedRoutes = [
    '/dashboard',
    '/master/dashboard', 
    '/child/dashboard',
    '/master/configuration',
  ]

  // Define public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/auth/callback',
    '/auth/zerodha/callback',
    '/auth/zerodha/child-callback', 
    '/auth/accept-invitation',
    '/demo/trading',
  ]

  // Check if current route is protected
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  )

  // Check if current route is public
  const isPublicRoute = publicRoutes.some(route => {
    return pathname === route || pathname.startsWith(route + '/')
  }) || pathname.startsWith('/api/')

  // If it's a protected route and user is not authenticated, redirect to home
  if (isProtectedRoute && !user) {
    const redirectUrl = new URL('/', request.url)
    return NextResponse.redirect(redirectUrl)
  }

  // If user is authenticated and trying to access auth pages, redirect to dashboard
  if (user && (pathname === '/' || pathname.startsWith('/auth/'))) {
    // Don't redirect auth callbacks
    if (pathname.startsWith('/auth/callback') || 
        pathname.startsWith('/auth/zerodha/') ||
        pathname.startsWith('/auth/accept-invitation')) {
      return supabaseResponse
    }
    
    const redirectUrl = new URL('/master/dashboard', request.url)
    return NextResponse.redirect(redirectUrl)
  }

  return supabaseResponse
}
